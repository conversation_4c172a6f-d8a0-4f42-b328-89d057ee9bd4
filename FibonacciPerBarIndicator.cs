//
// Copyright (C) 2025, NinjaTrader LLC <www.ninjatrader.com>.
// NinjaTrader reserves the right to modify or overwrite this NinjaScript component with each release.
//
#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds indicators in this folder and is required. Do not change it.
namespace NinjaTrader.NinjaScript.Indicators
{
	public class FibonacciPerBarIndicator : Indicator
	{
		private double[] fibLevels = { 1.1, 1.08, 1.0, 0.9, 0.1, 0.0, -0.08, -0.1 };
		private Brush[] fibColors;
		
		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description					= @"Fibonacci levels drawn per bar based on high/low range";
				Name						= "FibonacciPerBarIndicator";
				Calculate					= Calculate.OnBarClose;
				IsOverlay					= true;
				DisplayInDataBox			= true;
				DrawOnPricePanel			= true;
				DrawHorizontalGridLines		= true;
				DrawVerticalGridLines		= true;
				PaintPriceMarkers			= true;
				ScaleJustification			= NinjaTrader.Gui.Chart.ScaleJustification.Right;
				//Disable this property if your indicator requires custom values that cumulate with each new market data event. 
				//See Help Guide for additional information.
				IsSuspendedWhileInactive	= true;
				
				// Input parameters
				ShowLevels					= true;
				ShowLabels					= true;
				LineWidth					= 1;
				LineTransparency			= 20;
				
				// Color inputs
				FibColor11					= Brushes.White;
				FibColor108					= Brushes.White;
				FibColor10					= Brushes.Green;
				FibColor09					= Brushes.Orange;
				FibColor01					= Brushes.Purple;
				FibColor00					= Brushes.Red;
				FibColorNeg08				= Brushes.White;
				FibColorNeg1				= Brushes.White;
				TextColor					= Brushes.White;
			}
			else if (State == State.SetDataLoaded)
			{
				// Initialize color array
				fibColors = new Brush[] 
				{
					FibColor11, FibColor108, FibColor10, FibColor09, 
					FibColor01, FibColor00, FibColorNeg08, FibColorNeg1
				};
			}
		}

		protected override void OnBarUpdate()
		{
			if (CurrentBar < 1)
				return;
				
			// Calculate price range for current bar
			double rangeHigh = High[0];
			double rangeLow = Low[0];
			double priceRange = rangeHigh - rangeLow;
			
			if (priceRange <= 0)
				return;
				
			// Draw Fibonacci levels if enabled
			if (ShowLevels)
			{
				for (int i = 0; i < fibLevels.Length; i++)
				{
					double fibLevel = fibLevels[i];
					double fibPrice = rangeLow + (priceRange * fibLevel);
					
					// Create horizontal line for this bar only
					string lineName = $"FibLine_{CurrentBar}_{i}";
					Draw.Line(this, lineName, 0, fibPrice, 1, fibPrice, 
						GetColorWithTransparency(fibColors[i], LineTransparency));
					
					// Add label if enabled
					if (ShowLabels)
					{
						string labelName = $"FibLabel_{CurrentBar}_{i}";
						string labelText = $"{fibLevel:F2} ({fibPrice:F3})";
						Draw.Text(this, labelName, labelText, 0, fibPrice, TextColor);
					}
				}
			}
		}
		
		private Brush GetColorWithTransparency(Brush originalBrush, int transparency)
		{
			if (originalBrush is SolidColorBrush solidBrush)
			{
				Color color = solidBrush.Color;
				byte alpha = (byte)(255 * (100 - transparency) / 100);
				return new SolidColorBrush(Color.FromArgb(alpha, color.R, color.G, color.B));
			}
			return originalBrush;
		}

		#region Properties
		[NinjaScriptProperty]
		[Display(Name="Show Levels", Description="Show Fibonacci Levels", Order=1, GroupName="Display Settings")]
		public bool ShowLevels
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Show Labels", Description="Show Level Labels", Order=2, GroupName="Display Settings")]
		public bool ShowLabels
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, 5)]
		[Display(Name="Line Width", Description="Line Width", Order=3, GroupName="Display Settings")]
		public int LineWidth
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, 100)]
		[Display(Name="Line Transparency", Description="Line Transparency", Order=4, GroupName="Display Settings")]
		public int LineTransparency
		{ get; set; }

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="1.1 Level Color", Description="1.1 Level Color", Order=1, GroupName="Colors")]
		public Brush FibColor11
		{ get; set; }

		[Browsable(false)]
		public string FibColor11Serializable
		{
			get { return Serialize.BrushToString(FibColor11); }
			set { FibColor11 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="1.08 Level Color", Description="1.08 Level Color", Order=2, GroupName="Colors")]
		public Brush FibColor108
		{ get; set; }

		[Browsable(false)]
		public string FibColor108Serializable
		{
			get { return Serialize.BrushToString(FibColor108); }
			set { FibColor108 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="1.0 Level Color", Description="1.0 Level Color", Order=3, GroupName="Colors")]
		public Brush FibColor10
		{ get; set; }

		[Browsable(false)]
		public string FibColor10Serializable
		{
			get { return Serialize.BrushToString(FibColor10); }
			set { FibColor10 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="0.9 Level Color", Description="0.9 Level Color", Order=4, GroupName="Colors")]
		public Brush FibColor09
		{ get; set; }

		[Browsable(false)]
		public string FibColor09Serializable
		{
			get { return Serialize.BrushToString(FibColor09); }
			set { FibColor09 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="0.1 Level Color", Description="0.1 Level Color", Order=5, GroupName="Colors")]
		public Brush FibColor01
		{ get; set; }

		[Browsable(false)]
		public string FibColor01Serializable
		{
			get { return Serialize.BrushToString(FibColor01); }
			set { FibColor01 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="0.0 Level Color", Description="0.0 Level Color", Order=6, GroupName="Colors")]
		public Brush FibColor00
		{ get; set; }

		[Browsable(false)]
		public string FibColor00Serializable
		{
			get { return Serialize.BrushToString(FibColor00); }
			set { FibColor00 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="-0.08 Level Color", Description="-0.08 Level Color", Order=7, GroupName="Colors")]
		public Brush FibColorNeg08
		{ get; set; }

		[Browsable(false)]
		public string FibColorNeg08Serializable
		{
			get { return Serialize.BrushToString(FibColorNeg08); }
			set { FibColorNeg08 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="-0.1 Level Color", Description="-0.1 Level Color", Order=8, GroupName="Colors")]
		public Brush FibColorNeg1
		{ get; set; }

		[Browsable(false)]
		public string FibColorNeg1Serializable
		{
			get { return Serialize.BrushToString(FibColorNeg1); }
			set { FibColorNeg1 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="Label Text Color", Description="Label Text Color", Order=9, GroupName="Colors")]
		public Brush TextColor
		{ get; set; }

		[Browsable(false)]
		public string TextColorSerializable
		{
			get { return Serialize.BrushToString(TextColor); }
			set { TextColor = Serialize.StringToBrush(value); }
		}
		#endregion
	}
}
